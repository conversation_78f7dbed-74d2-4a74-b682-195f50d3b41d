package main

import (
	"encoding/json"
	"log"
	"strings"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// Global variable to store periodic data
var periodicDataStore PeriodicDataStore
var periodicDataMutex sync.Mutex

// MessageHandlers contains all message handling logic
type MessageHandlers struct {
	dataProcessor *DataProcessor
	validator     *Validator
	serverClient  mqtt.Client
	clientClient  mqtt.Client
}

// NewMessageHandlers creates a new message handlers instance
func NewMessageHandlers(serverClient, clientClient mqtt.Client) *MessageHandlers {
	return &MessageHandlers{
		dataProcessor: NewDataProcessor(),
		validator:     NewValidator(),
		serverClient:  serverClient,
		clientClient:  clientClient,
	}
}

// ServerMessageHandler handles messages for server topics
func (mh *MessageHandlers) ServerMessageHandler(client mqtt.Client, msg mqtt.Message) {
	log.Printf("Received on server topic '%s': %s\n", msg.Topic(), string(msg.Payload()))

	switch msg.Topic() {
	case TopicArgusGetConfig:
		mh.HandleGetConfigRequest(client, msg)
	case TopicArgusReqControlData:
		mh.HandleControlRequest(msg)
	case TopicArgusPullSensorData:
		mh.HandlePullSensorData(msg)
	}
}

// ClientMessageHandler handles messages for client topics
func (mh *MessageHandlers) ClientMessageHandler(client mqtt.Client, msg mqtt.Message) {
	log.Printf("Received on client topic '%s': %s\n", msg.Topic(), string(msg.Payload()))

	switch msg.Topic() {
	case TopicFirmwareReadDataPeriodicRes:
		mh.HandlePeriodicData(msg)
	case TopicFirmwareWriteDataRes:
		mh.HandleWriteResponse(msg)
	case TopicFirmwareReadDataRes:
		mh.HandleReadResponse(msg)
	}
}

// HandlePeriodicData handles periodic data messages
func (mh *MessageHandlers) HandlePeriodicData(msg mqtt.Message) {
	var periodicDataIO PeriodicDataIO
	if err := json.Unmarshal(msg.Payload(), &periodicDataIO); err != nil {
		log.Printf("Error parsing periodic data: %v\n", err)
		return
	}

	// Transform the data using the helper function
	cloudPeriodicData := mh.dataProcessor.TransformPeriodicDataToCloud(periodicDataIO)

	// Store the data with timestamp from cloudPeriodicData
	periodicDataMutex.Lock()
	timestamp := time.Now() // Default to current time
	if cloudPeriodicData.Timestamp > 0 {
		// Convert milliseconds to time.Time
		timestamp = time.Unix(0, cloudPeriodicData.Timestamp*int64(time.Millisecond))
	}
	periodicDataStore = PeriodicDataStore{
		data:      cloudPeriodicData,
		timestamp: timestamp,
	}
	periodicDataMutex.Unlock()

	// Publish immediately to periodic topic
	mh.dataProcessor.PublishJSON(mh.serverClient, TopicArgusPeriodic, cloudPeriodicData)
}

// HandleWriteResponse handles write response messages
func (mh *MessageHandlers) HandleWriteResponse(msg mqtt.Message) {
	log.Printf("Received write response:\n%s\n", string(msg.Payload()))

	// Use the common data processing function
	err := mh.dataProcessor.ProcessAndPublishPeriodicData(msg.Payload(), mh.serverClient, TopicArgusResponseControlData)
	if err != nil {
		log.Printf("Error processing write response: %v\n", err)
		return
	}

	log.Printf("✓ Successfully republished write response to %s\n", TopicArgusResponseControlData)
}

// HandleReadResponse handles read response messages
func (mh *MessageHandlers) HandleReadResponse(msg mqtt.Message) {
	log.Printf("Received read response on topic '%s':\n%s\n", msg.Topic(), string(msg.Payload()))

	// Use the common data processing function
	err := mh.dataProcessor.ProcessAndPublishPeriodicData(msg.Payload(), mh.serverClient, TopicArgusPeriodic)
	if err != nil {
		log.Printf("Error processing read response: %v\n", err)
		return
	}

	log.Printf("✓ Successfully republished read response to %s\n", TopicArgusPeriodic)
}

// HandlePullSensorData handles pull sensor data requests
func (mh *MessageHandlers) HandlePullSensorData(msg mqtt.Message) {
	var pullReq ReadRequest
	if err := json.Unmarshal(msg.Payload(), &pullReq); err != nil {
		log.Printf("Error parsing pull sensor request: %v\n", err)
		return
	}

	// Check if we have recent periodic data using PDS time from env var
	periodicDataMutex.Lock()
	hasRecentData := time.Since(periodicDataStore.timestamp) <= GetPDSTime()
	storedData := periodicDataStore.data
	periodicDataMutex.Unlock()

	if hasRecentData {
		log.Println("returning recent periodic data")
		// Filter the stored data based on request type
		filteredData := mh.dataProcessor.FilterPeriodicData(storedData, pullReq)

		// Check if filtered data has any zones
		if len(filteredData.Zones) > 0 {
			mh.dataProcessor.PublishJSON(mh.serverClient, TopicArgusPeriodic, filteredData)
			return
		}
		log.Println("filtered data has no zones, proceeding with normal request flow")
	}
	log.Println("no recent periodic data found, proceeding with normal request flow")

	// Handle different request types
	switch pullReq.RequestType {
	case "zone":
		mh.handleZoneRequest(pullReq)
	default:
		mh.handleOtherRequests(pullReq)
	}
}

// handleZoneRequest handles zone-specific pull requests
func (mh *MessageHandlers) handleZoneRequest(pullReq ReadRequest) {
	// Get all cards for the requested zone
	zoneId := strings.TrimSpace(pullReq.Value)
	cards := GetCardsForZone(zoneId)

	if len(cards) == 0 {
		log.Printf("No cards found for zone: %s", zoneId)
		return
	}

	log.Printf("Found %d cards for zone %s: %v", len(cards), zoneId, cards)

	// Create cards request
	cardsReq := CardsReadRequest{
		RequestType: "cards",
		Values:      cards,
	}

	mh.dataProcessor.PublishJSON(mh.clientClient, TopicFirmwareReadDataReq, cardsReq)
}

// handleOtherRequests handles other types of pull requests
func (mh *MessageHandlers) handleOtherRequests(pullReq ReadRequest) {
	// Handle other request types (card, channel, all) with existing logic
	readReq := ReadRequest{
		RequestType: pullReq.RequestType,
		Value:       pullReq.Value,
	}

	// Adjust value format based on request type
	switch pullReq.RequestType {
	case "card":
		if parts := strings.Split(pullReq.Value, "/"); len(parts) == 2 {
			readReq.Value = parts[1]
		}
	case "channel":
		if parts := strings.Split(pullReq.Value, "/"); len(parts) == 3 {
			readReq.Value = parts[1] + "/" + parts[2]
		}
	}

	mh.dataProcessor.PublishJSON(mh.clientClient, TopicFirmwareReadDataReq, readReq)
}

// HandleControlRequest handles control requests
func (mh *MessageHandlers) HandleControlRequest(msg mqtt.Message) {
	var controlReq ControlRequest
	if err := json.Unmarshal(msg.Payload(), &controlReq); err != nil {
		log.Printf("Error parsing control request: %v\n", err)
		return
	}

	// Validate the control request
	if err := mh.validator.ValidateControlRequest(controlReq); err != nil {
		log.Printf("Control request validation failed: %v", err)
		return
	}

	// Create write request using channelName from the incoming message
	writeReq := WriteRequest{
		Cards: []struct {
			CardID   string `json:"cardId"`
			Channels []struct {
				ChannelID   string      `json:"channelId"`
				ChannelName string      `json:"channelName"`
				Value       interface{} `json:"value"`
				Unit        string      `json:"unit"`
			} `json:"channels"`
		}{
			{
				CardID: controlReq.CardID,
				Channels: []struct {
					ChannelID   string      `json:"channelId"`
					ChannelName string      `json:"channelName"`
					Value       interface{} `json:"value"`
					Unit        string      `json:"unit"`
				}{
					{
						ChannelID:   controlReq.ChannelID,
						ChannelName: controlReq.ChannelName,
						Value:       controlReq.Value,
						Unit:        "C", // Default unit, can be made configurable
					},
				},
			},
		},
	}

	// Publish write request using clientClient since it's not an argus/ topic
	writeReqJSON, err := json.MarshalIndent(writeReq, "", "  ")
	if err != nil {
		log.Printf("Error marshaling write request: %v\n", err)
		return
	}

	log.Printf("Publishing write request to %s:\n%s\n", TopicFirmwareWriteDataReq, string(writeReqJSON))

	if token := mh.clientClient.Publish(TopicFirmwareWriteDataReq, GetQoS(), false, writeReqJSON); token.Wait() && token.Error() != nil {
		log.Printf("Failed to publish write request: %v\n", token.Error())
	} else {
		log.Printf("✓ Published write request to %s\n", TopicFirmwareWriteDataReq)
	}
}

// HandleGetConfigRequest handles get config requests
func (mh *MessageHandlers) HandleGetConfigRequest(client mqtt.Client, msg mqtt.Message) {
	var getConfigReq GetConfigRequest
	if err := json.Unmarshal(msg.Payload(), &getConfigReq); err != nil {
		log.Printf("Error parsing getConfig request: %v\n", err)
		return
	}

	log.Printf("Processing getConfig request: type=%s, value=%s\n",
		getConfigReq.RequestType, getConfigReq.Value)

	// Load IO firmware config
	ioConfig, err := LoadIOFirmwareConfig(GetIOConfigPath())
	if err != nil {
		log.Printf("Error loading IO firmware config: %v\n", err)
		return
	}

	// Transform to cloud platform format
	cloudConfig := mh.dataProcessor.TransformToCloudPlatformConfig(ioConfig)

	// Filter based on request type
	if getConfigReq.RequestType == "zone" {
		cloudConfig = mh.filterConfigByZone(cloudConfig, getConfigReq.Value)
	}
	// For "all" request type, we return all zones (no filtering needed)

	// Publish the transformed config
	mh.dataProcessor.PublishJSON(client, TopicArgusConfig, cloudConfig)
	log.Printf("✓ Published config response to '%s'\n", TopicArgusConfig)
}

// filterConfigByZone filters cloud config by zone
func (mh *MessageHandlers) filterConfigByZone(cloudConfig *CloudPlatformConfig, value string) *CloudPlatformConfig {
	// Extract zone ID from value (format: "* / ${zoneId}" or just zoneId)
	zoneId := strings.TrimSpace(value)
	if strings.Contains(zoneId, "/") {
		parts := strings.Split(zoneId, "/")
		if len(parts) > 1 {
			zoneId = strings.TrimSpace(parts[1])
		}
	}
	zoneId = strings.Trim(zoneId, "${}")

	// Filter zones
	filteredZones := []CloudZone{}
	for _, zone := range cloudConfig.Zones {
		if zone.ZoneID == zoneId {
			filteredZones = append(filteredZones, zone)
			break
		}
	}
	cloudConfig.Zones = filteredZones
	return cloudConfig
}

// PublishInitialConfig publishes initial configuration on startup
func (mh *MessageHandlers) PublishInitialConfig(client mqtt.Client) {
	// Load and publish initial config on startup
	ioConfig, err := LoadIOFirmwareConfig(GetIOConfigPath())
	if err != nil {
		log.Printf("Warning: Cannot load IO firmware config for initial publish: %v\n", err)
		return
	}

	cloudConfig := mh.dataProcessor.TransformToCloudPlatformConfig(ioConfig)
	mh.dataProcessor.PublishJSON(client, TopicArgusConfig, cloudConfig)
	log.Printf("✓ Published initial config to '%s'\n", TopicArgusConfig)
}
