package main

import (
	"fmt"
	"log"
)

// ValidationError represents a validation error
type ValidationError struct {
	Field   string
	Message string
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error for %s: %s", e.Field, e.Message)
}

// Validator handles validation operations
type Validator struct{}

// NewValidator creates a new validator
func NewValidator() *Validator {
	return &Validator{}
}

// ValidateControlRequest validates a control request
func (v *Validator) ValidateControlRequest(req ControlRequest) error {
	// Validation 1: Check if cardId belongs to the requested zoneId
	cardsToZonesMapping := GetCardsToZonesMapping()
	expectedZoneID, cardExists := cardsToZonesMapping[req.CardID]
	if !cardExists {
		log.Printf("Ignoring control request: cardId '%s' not found in mapping", req.CardID)
		return ValidationError{
			Field:   "cardId",
			Message: fmt.Sprintf("cardId '%s' not found in mapping", req.CardID),
		}
	}
	if expectedZoneID != req.ZoneID {
		log.Printf("Ignoring control request: cardId '%s' does not belongs to zone '%s'",
			req.CardID, req.ZoneID)
		return ValidationError{
			Field:   "zoneId",
			Message: fmt.Sprintf("cardId '%s' does not belong to zone '%s'", req.CardID, req.ZoneID),
		}
	}

	// Validation 2: Check if zoneId belongs to the requested hubId
	zoneIdToHubsMapping := GetZoneIdToHubsMapping()
	expectedHubID, zoneExists := zoneIdToHubsMapping[req.ZoneID]
	if !zoneExists {
		log.Printf("Ignoring control request: zoneId '%s' not found in hub mapping", req.ZoneID)
		return ValidationError{
			Field:   "zoneId",
			Message: fmt.Sprintf("zoneId '%s' not found in hub mapping", req.ZoneID),
		}
	}
	if expectedHubID != req.HubID {
		log.Printf("Ignoring control request: zoneId '%s' does not belong to hub '%s'",
			req.ZoneID, req.HubID)
		return ValidationError{
			Field:   "hubId",
			Message: fmt.Sprintf("zoneId '%s' does not belong to hub '%s'", req.ZoneID, req.HubID),
		}
	}

	log.Printf("✓ Validation passed: cardId '%s' belongs to zoneId '%s' and zoneId belongs to hubId '%s'",
		req.CardID, req.ZoneID, req.HubID)

	return nil
}
