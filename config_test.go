package main

import (
	"encoding/json"
	"os"
	"strings"
	"testing"
	"time"
)

func TestConfigLoading(t *testing.T) {
	// Test loading production config
	err := loadConfigs()
	if err != nil {
		t.Fatalf("Failed to load production configs: %v", err)
	}

	// Test that the mappings are loaded correctly from production config
	cardZones := getCardsToZonesMapping()
	zoneNames := getZoneIdToNamesMapping()

	// Check that we have the expected mappings
	if len(cardZones) == 0 {
		t.<PERSON>rror("Card zones mapping should not be empty")
	}
	if len(zoneNames) == 0 {
		t.<PERSON>rror("Zone names mapping should not be empty")
	}

	// Check specific mappings from production config
	if cardZones["CD1"] != "north_house" {
		t.<PERSON><PERSON>("Expected CD1 to map to 'north_house', got '%s'", cardZones["CD1"])
	}
	if zoneNames["north_house"] != "North House" {
		t.<PERSON>("Expected 'north_house' to map to 'North House', got '%s'", zoneNames["north_house"])
	}
}

func TestTestConfigLoading(t *testing.T) {
	// Set testing environment
	os.Setenv("TESTING", "true")
	defer os.Unsetenv("TESTING")

	// Test loading test config
	err := loadConfigs()
	if err != nil {
		t.Fatalf("Failed to load test configs: %v", err)
	}

	// Test that the mappings are loaded correctly from test config
	cardZones := getCardsToZonesMapping()
	zoneNames := getZoneIdToNamesMapping()

	// Check that we have the expected mappings
	if len(cardZones) == 0 {
		t.Error("Card zones mapping should not be empty")
	}
	if len(zoneNames) == 0 {
		t.Error("Zone names mapping should not be empty")
	}

	// Check specific mappings from test config (test config uses different zone names)
	if cardZones["CD1"] != "zone_a" {
		t.Errorf("Expected CD1 to map to 'zone_a', got '%s'", cardZones["CD1"])
	}
	if zoneNames["zone_a"] != "Zone A" {
		t.Errorf("Expected 'zone_a' to map to 'Zone A', got '%s'", zoneNames["zone_a"])
	}
}

func TestFallbackMappings(t *testing.T) {
	// Save original mapping config
	originalMappingConfig := mappingConfig

	// Test with empty mapping config (should use fallback)
	mappingConfig = MappingConfig{}

	cardZones := getCardsToZonesMapping()
	zoneNames := getZoneIdToNamesMapping()
	zoneHubs := getZoneIdToHubsMapping()

	// Check that fallback mappings are used
	if len(cardZones) == 0 {
		t.Error("Fallback card zones mapping should not be empty")
	}
	if len(zoneNames) == 0 {
		t.Error("Fallback zone names mapping should not be empty")
	}
	if len(zoneHubs) == 0 {
		t.Error("Fallback zone hubs mapping should not be empty")
	}

	// Check specific fallback mappings
	if cardZones["CD1"] != "north_house" {
		t.Errorf("Expected fallback CD1 to map to 'north_house', got '%s'", cardZones["CD1"])
	}
	if zoneNames["north_house"] != "North House" {
		t.Errorf("Expected fallback 'north_house' to map to 'North House', got '%s'", zoneNames["north_house"])
	}
	if zoneHubs["north_house"] != "hub1" {
		t.Errorf("Expected fallback 'north_house' to map to 'hub1', got '%s'", zoneHubs["north_house"])
	}

	// Restore original mapping config
	mappingConfig = originalMappingConfig
}

func TestZoneHubMappings(t *testing.T) {
	// Test loading production config
	err := loadConfigs()
	if err != nil {
		t.Fatalf("Failed to load production configs: %v", err)
	}

	// Test that the hub mappings are loaded correctly from production config
	zoneHubs := getZoneIdToHubsMapping()

	// Check that we have the expected mappings
	if len(zoneHubs) == 0 {
		t.Error("Zone hubs mapping should not be empty")
	}

	// Check specific mappings from production config
	if zoneHubs["north_house"] != "hub1" {
		t.Errorf("Expected 'north_house' to map to 'hub1', got '%s'", zoneHubs["north_house"])
	}
	if zoneHubs["south_house"] != "hub1" {
		t.Errorf("Expected 'south_house' to map to 'hub1', got '%s'", zoneHubs["south_house"])
	}
	if zoneHubs["propagation_house"] != "hub1" {
		t.Errorf("Expected 'propagation_house' to map to 'hub1', got '%s'", zoneHubs["propagation_house"])
	}
}

func TestHubIdInConfigOutput(t *testing.T) {
	// Load configs
	err := loadConfigs()
	if err != nil {
		t.Fatalf("Failed to load configs: %v", err)
	}

	// Create a mock IO firmware config
	mockIOConfig := &IOFirmwareConfig{
		ZoneName: "test_zone",
		DeviceID: "test_device",
		Ports: []Port{
			{
				Cards: []Card{
					{
						CardID: "CD1",
						Channels: []Channel{
							{
								ChannelID:   "CH1",
								ChannelName: "Test Channel",
								Type:        "input",
								SignalType:  "digital",
							},
						},
					},
				},
			},
		},
	}

	// Transform to cloud platform config
	cloudConfig := transformToCloudPlatformConfig(mockIOConfig)

	// Convert to JSON to check the output
	configJSON, err := json.MarshalIndent(cloudConfig, "", "  ")
	if err != nil {
		t.Fatalf("Failed to marshal config: %v", err)
	}

	configStr := string(configJSON)

	// Check that hubId is present in the JSON output
	if !strings.Contains(configStr, `"hubId"`) {
		t.Error("Expected hubId field to be present in config JSON output")
	}

	// Parse back to verify structure
	var parsedConfig CloudPlatformConfig
	err = json.Unmarshal(configJSON, &parsedConfig)
	if err != nil {
		t.Fatalf("Failed to unmarshal config: %v", err)
	}

	// Check that zones have hubId
	if len(parsedConfig.Zones) == 0 {
		t.Error("Expected at least one zone in config")
	} else {
		zone := parsedConfig.Zones[0]
		if zone.HubID == "" {
			t.Error("Expected zone to have a hubId")
		}
		if zone.ZoneID == "north_house" && zone.HubID != "hub1" {
			t.Errorf("Expected north_house to have hubId 'hub1', got '%s'", zone.HubID)
		}
	}
}

func TestHubIdInPeriodicData(t *testing.T) {
	// Load configs
	err := loadConfigs()
	if err != nil {
		t.Fatalf("Failed to load configs: %v", err)
	}

	// Create mock periodic data
	mockPeriodicData := PeriodicDataIO{
		Timestamp: time.Now().UnixMilli(),
		ZoneID:    "test_zone",
		Cards: []PeriodicCard{
			{
				CardID:   "CD1",
				CardName: "CD1",
				Channels: []PeriodicChannel{
					{
						ChannelID:   "CH1",
						ChannelName: "Test Channel",
						Value:       25.5,
						Unit:        "C",
						Status:      "OK",
					},
				},
			},
		},
	}

	// Transform to cloud format
	cloudData := transformPeriodicDataToCloud(mockPeriodicData)

	// Convert to JSON to check the output
	dataJSON, err := json.MarshalIndent(cloudData, "", "  ")
	if err != nil {
		t.Fatalf("Failed to marshal periodic data: %v", err)
	}

	dataStr := string(dataJSON)

	// Check that hubId is present in the JSON output
	if !strings.Contains(dataStr, `"hubId"`) {
		t.Error("Expected hubId field to be present in periodic data JSON output")
	}

	// Check that zones have hubId
	if len(cloudData.Zones) == 0 {
		t.Error("Expected at least one zone in periodic data")
	} else {
		zone := cloudData.Zones[0]
		if zone.HubID == "" {
			t.Error("Expected zone to have a hubId in periodic data")
		}
	}
}

func TestIOConfigPath(t *testing.T) {
	// Test production path (default)
	os.Unsetenv("TESTING")
	prodPath := getIOConfigPath()
	expectedProdPath := "firmware-config.json"
	if prodPath != expectedProdPath {
		t.Errorf("Expected production IO config path to be '%s', got '%s'", expectedProdPath, prodPath)
	}

	// Test testing path
	os.Setenv("TESTING", "true")
	testPath := getIOConfigPath()
	expectedTestPath := "test/test-firmware-config.json"
	if testPath != expectedTestPath {
		t.Errorf("Expected test IO config path to be '%s', got '%s'", expectedTestPath, testPath)
	}

	// Clean up
	os.Unsetenv("TESTING")
}

func TestIOConfigLoading(t *testing.T) {
	// Test loading production IO config
	os.Unsetenv("TESTING")
	prodConfig, err := loadIOFirmwareConfig(getIOConfigPath())
	if err != nil {
		t.Fatalf("Failed to load production IO config: %v", err)
	}
	if prodConfig == nil {
		t.Error("Production IO config should not be nil")
	}

	// Test loading test IO config
	os.Setenv("TESTING", "true")
	defer os.Unsetenv("TESTING")

	testConfig, err := loadIOFirmwareConfig(getIOConfigPath())
	if err != nil {
		t.Fatalf("Failed to load test IO config: %v", err)
	}
	if testConfig == nil {
		t.Error("Test IO config should not be nil")
	}

	// Verify they are different configs (different zone names or device IDs)
	if prodConfig != nil && testConfig != nil && prodConfig.ZoneName == testConfig.ZoneName && prodConfig.DeviceID == testConfig.DeviceID {
		t.Log("Warning: Production and test IO configs appear to be identical")
	}

	// Verify test config has expected structure
	if len(testConfig.Ports) == 0 {
		t.Error("Test IO config should have at least one port")
	}
}

func TestMappingConfigLoading(t *testing.T) {
	// Test loading production mapping config
	os.Unsetenv("TESTING")
	err := loadMappingConfig()
	if err != nil {
		t.Fatalf("Failed to load production mapping config: %v", err)
	}

	// Verify production mappings
	cardZones := getCardsToZonesMapping()
	zoneNames := getZoneIdToNamesMapping()
	zoneHubs := getZoneIdToHubsMapping()

	if len(cardZones) == 0 {
		t.Error("Production card zones mapping should not be empty")
	}
	if len(zoneNames) == 0 {
		t.Error("Production zone names mapping should not be empty")
	}
	if len(zoneHubs) == 0 {
		t.Error("Production zone hubs mapping should not be empty")
	}

	// Check specific production mappings
	if cardZones["CD1"] != "north_house" {
		t.Errorf("Expected CD1 to map to 'north_house', got '%s'", cardZones["CD1"])
	}
	if zoneNames["north_house"] != "North House" {
		t.Errorf("Expected 'north_house' to map to 'North House', got '%s'", zoneNames["north_house"])
	}
	if zoneHubs["north_house"] != "hub1" {
		t.Errorf("Expected 'north_house' to map to 'hub1', got '%s'", zoneHubs["north_house"])
	}

	// Test loading test mapping config
	os.Setenv("TESTING", "true")
	defer os.Unsetenv("TESTING")

	err = loadMappingConfig()
	if err != nil {
		t.Fatalf("Failed to load test mapping config: %v", err)
	}

	// Verify test mappings
	cardZones = getCardsToZonesMapping()
	zoneNames = getZoneIdToNamesMapping()
	zoneHubs = getZoneIdToHubsMapping()

	if len(cardZones) == 0 {
		t.Error("Test card zones mapping should not be empty")
	}
	if len(zoneNames) == 0 {
		t.Error("Test zone names mapping should not be empty")
	}
	if len(zoneHubs) == 0 {
		t.Error("Test zone hubs mapping should not be empty")
	}

	// Check specific test mappings
	if cardZones["CD1"] != "zone_a" {
		t.Errorf("Expected CD1 to map to 'zone_a', got '%s'", cardZones["CD1"])
	}
	if zoneNames["zone_a"] != "Zone A" {
		t.Errorf("Expected 'zone_a' to map to 'Zone A', got '%s'", zoneNames["zone_a"])
	}
	if zoneHubs["zone_a"] != "hub1" {
		t.Errorf("Expected 'zone_a' to map to 'hub1', got '%s'", zoneHubs["zone_a"])
	}
}

func TestMappingConfigPath(t *testing.T) {
	// Test production path (default)
	os.Unsetenv("TESTING")
	prodPath := getMappingConfigPath()
	expectedProdPath := "mapping-config.json"
	if prodPath != expectedProdPath {
		t.Errorf("Expected production mapping config path to be '%s', got '%s'", expectedProdPath, prodPath)
	}

	// Test testing path
	os.Setenv("TESTING", "true")
	testPath := getMappingConfigPath()
	expectedTestPath := "test/test-mapping-config.json"
	if testPath != expectedTestPath {
		t.Errorf("Expected test mapping config path to be '%s', got '%s'", expectedTestPath, testPath)
	}

	// Clean up
	os.Unsetenv("TESTING")
}

func TestGetCardsForZone(t *testing.T) {
	// Set testing environment to use test configs
	os.Setenv("TESTING", "true")
	defer os.Unsetenv("TESTING")

	// Load configs
	err := loadConfigs()
	if err != nil {
		t.Fatalf("Failed to load configs: %v", err)
	}

	// Test getting cards for zone_a
	cards := getCardsForZone("zone_a")
	expectedCards := []string{"CD1", "CD2", "CD3", "CD4", "CD5"}

	if len(cards) != len(expectedCards) {
		t.Errorf("Expected %d cards for zone_a, got %d", len(expectedCards), len(cards))
	}

	// Check that all expected cards are present
	cardMap := make(map[string]bool)
	for _, card := range cards {
		cardMap[card] = true
	}

	for _, expectedCard := range expectedCards {
		if !cardMap[expectedCard] {
			t.Errorf("Expected card %s not found in zone_a cards", expectedCard)
		}
	}

	// Test getting cards for zone_b
	cards = getCardsForZone("zone_b")
	expectedCards = []string{"CD6", "CD7", "CD8"}

	if len(cards) != len(expectedCards) {
		t.Errorf("Expected %d cards for zone_b, got %d", len(expectedCards), len(cards))
	}

	// Test getting cards for non-existent zone
	cards = getCardsForZone("non_existent_zone")
	if len(cards) != 0 {
		t.Errorf("Expected 0 cards for non-existent zone, got %d", len(cards))
	}
}

func TestZonePullSensorDataRequest(t *testing.T) {
	// Set testing environment to use test configs
	os.Setenv("TESTING", "true")
	defer os.Unsetenv("TESTING")

	// Load configs
	err := loadConfigs()
	if err != nil {
		t.Fatalf("Failed to load configs: %v", err)
	}

	// Test zone request structure
	zoneRequest := ReadRequest{
		RequestType: "zone",
		Value:       "zone_a",
	}

	// Verify the request can be marshaled to JSON
	requestJSON, err := json.MarshalIndent(zoneRequest, "", "  ")
	if err != nil {
		t.Fatalf("Failed to marshal zone request: %v", err)
	}

	// Verify the JSON structure
	expectedJSON := `{
  "requestType": "zone",
  "value": "zone_a"
}`

	if strings.TrimSpace(string(requestJSON)) != strings.TrimSpace(expectedJSON) {
		t.Errorf("Zone request JSON mismatch.\nExpected:\n%s\nGot:\n%s", expectedJSON, string(requestJSON))
	}

	// Test cards response structure
	cards := getCardsForZone("zone_a")
	cardsResponse := CardsReadRequest{
		RequestType: "cards",
		Values:      cards,
	}

	// Verify the response can be marshaled to JSON
	responseJSON, err := json.MarshalIndent(cardsResponse, "", "  ")
	if err != nil {
		t.Fatalf("Failed to marshal cards response: %v", err)
	}

	// Verify the JSON contains expected structure
	if !strings.Contains(string(responseJSON), `"requestType": "cards"`) {
		t.Error("Cards response should contain requestType: cards")
	}
	if !strings.Contains(string(responseJSON), `"values"`) {
		t.Error("Cards response should contain values field")
	}
	if !strings.Contains(string(responseJSON), `"CD1"`) {
		t.Error("Cards response should contain CD1 for zone_a")
	}

	t.Logf("Zone request JSON:\n%s", string(requestJSON))
	t.Logf("Cards response JSON:\n%s", string(responseJSON))
}

func TestControlRequestValidation(t *testing.T) {
	// Set testing environment to use test configs
	os.Setenv("TESTING", "true")
	defer os.Unsetenv("TESTING")

	// Load configs
	err := loadConfigs()
	if err != nil {
		t.Fatalf("Failed to load configs: %v", err)
	}

	// Test valid control request (using test config data)
	validReq := ControlRequest{
		HubID:       "hub1",
		ZoneID:      "zone_a",
		CardID:      "CD1",
		ChannelID:   "CH1",
		ChannelName: "Test Channel",
		Value:       1,
	}

	// Get mappings to verify test data
	cardsToZones := getCardsToZonesMapping()
	zoneToHubs := getZoneIdToHubsMapping()

	// Verify test data is valid
	if cardsToZones["CD1"] != "zone_a" {
		t.Fatalf("Test setup error: CD1 should map to zone_a, got %s", cardsToZones["CD1"])
	}
	if zoneToHubs["zone_a"] != "hub1" {
		t.Fatalf("Test setup error: zone_a should map to hub1, got %s", zoneToHubs["zone_a"])
	}

	// Test 1: Valid request should pass validation
	t.Run("ValidRequest", func(t *testing.T) {
		// Check card belongs to zone
		expectedZone, cardExists := cardsToZones[validReq.CardID]
		if !cardExists {
			t.Errorf("Card %s should exist in mapping", validReq.CardID)
		}
		if expectedZone != validReq.ZoneID {
			t.Errorf("Card %s should belong to zone %s, got %s", validReq.CardID, validReq.ZoneID, expectedZone)
		}

		// Check zone belongs to hub
		expectedHub, zoneExists := zoneToHubs[validReq.ZoneID]
		if !zoneExists {
			t.Errorf("Zone %s should exist in hub mapping", validReq.ZoneID)
		}
		if expectedHub != validReq.HubID {
			t.Errorf("Zone %s should belong to hub %s, got %s", validReq.ZoneID, validReq.HubID, expectedHub)
		}
	})

	// Test 2: Invalid card-zone mapping
	t.Run("InvalidCardZoneMapping", func(t *testing.T) {
		invalidReq := validReq
		invalidReq.ZoneID = "zone_b" // CD1 belongs to zone_a, not zone_b

		expectedZone, cardExists := cardsToZones[invalidReq.CardID]
		if !cardExists {
			t.Errorf("Card %s should exist in mapping", invalidReq.CardID)
		}
		if expectedZone == invalidReq.ZoneID {
			t.Errorf("This test expects card %s to NOT belong to zone %s", invalidReq.CardID, invalidReq.ZoneID)
		}
	})

	// Test 3: Invalid zone-hub mapping
	t.Run("InvalidZoneHubMapping", func(t *testing.T) {
		invalidReq := validReq
		invalidReq.HubID = "hub2" // zone_a belongs to hub1, not hub2

		expectedHub, zoneExists := zoneToHubs[invalidReq.ZoneID]
		if !zoneExists {
			t.Errorf("Zone %s should exist in hub mapping", invalidReq.ZoneID)
		}
		if expectedHub == invalidReq.HubID {
			t.Errorf("This test expects zone %s to NOT belong to hub %s", invalidReq.ZoneID, invalidReq.HubID)
		}
	})

	// Test 4: Non-existent card
	t.Run("NonExistentCard", func(t *testing.T) {
		invalidReq := validReq
		invalidReq.CardID = "CD99" // Non-existent card

		_, cardExists := cardsToZones[invalidReq.CardID]
		if cardExists {
			t.Errorf("Card %s should not exist in mapping", invalidReq.CardID)
		}
	})

	// Test 5: Non-existent zone
	t.Run("NonExistentZone", func(t *testing.T) {
		invalidReq := validReq
		invalidReq.ZoneID = "non_existent_zone"

		_, zoneExists := zoneToHubs[invalidReq.ZoneID]
		if zoneExists {
			t.Errorf("Zone %s should not exist in hub mapping", invalidReq.ZoneID)
		}
	})
}
