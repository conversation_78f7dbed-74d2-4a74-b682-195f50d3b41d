package main

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/joho/godotenv"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

const (
	defaultQoS          = 2     // Default QoS level
	defaultPDSTime      = 5     // Default PDS time in seconds
	defaultLogTimestamp = false // Default log timestamp
)

// Global variables for configuration values
var (
	currentQoS     byte
	currentPDSTime time.Duration
	logTimestamp   bool
)

// MQTT topic variables - loaded from config files
var (
	// Server topics
	TopicArgusGetConfig           string
	TopicArgusReqControlData      string
	TopicArgusPullSensorData      string
	TopicArgusConfig              string
	TopicArgusPeriodic            string
	TopicArgusResponseControlData string

	// Client topics
	TopicFirmwareWriteDataReq        string
	TopicFirmwareWriteDataRes        string
	TopicFirmwareReadDataReq         string
	TopicFirmwareReadDataRes         string
	TopicFirmwareReadDataPeriodicRes string
)

// MQTT Configuration structures
type MQTTConfig struct {
	Broker   string `json:"broker"`
	Port     int    `json:"port"`
	Protocol string `json:"protocol"`
	Username string `json:"username"`
	Password string `json:"password"`
	ClientID string `json:"clientId"`
}

type Config struct {
	MQTT            MQTTConfig        `json:"mqtt"`
	SubscribeTopics map[string]string `json:"subscribeTopics"`
	PublishTopics   map[string]string `json:"publishTopics"`
}

type MappingConfig struct {
	CardZones   map[string]string `json:"cardZones"`
	ZoneIdNames map[string]string `json:"zoneIdNames"`
	ZoneIdHubs  map[string]string `json:"zoneIdHubs"`
}

// IO Firmware Configuration structures
type ValueConfig struct {
	Address string `json:"address"`
	Bytes   int    `json:"bytes"`
}

type RangeConfig struct {
	Min float64 `json:"min"`
	Max float64 `json:"max"`
}

type Channel struct {
	ChannelID   string       `json:"channelId"`
	ChannelName string       `json:"channelName"`
	Type        string       `json:"type"`
	SignalType  string       `json:"signalType"`
	Value       ValueConfig  `json:"value"`
	Range       *RangeConfig `json:"range,omitempty"`
}

type Card struct {
	CardID     string    `json:"cardId"`
	ModbusAddr string    `json:"modbusAddr"`
	Channels   []Channel `json:"channels"`
}

type Port struct {
	BaudRate int    `json:"baudRate"`
	Parity   string `json:"parity"`
	Cards    []Card `json:"cards"`
}

type MQTTTopics struct {
	ReadReqTopic        string `json:"readReqTopic"`
	ReadResTopic        string `json:"readResTopic"`
	WriteReqTopic       string `json:"writeReqTopic"`
	WriteResTopic       string `json:"writeResTopic"`
	PeriodicResTopic    string `json:"periodicResTopic"`
	PeriodicityInterval int    `json:"periodicityInterval"`
}

type MQTTConf struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
}

type IOFirmwareConfig struct {
	ZoneName   string     `json:"zoneName"`
	DeviceID   string     `json:"deviceId"`
	MQTTConf   MQTTConf   `json:"mqttConf"`
	MQTTTopics MQTTTopics `json:"mqttTopics"`
	Ports      []Port     `json:"ports"`
}

// Cloud Platform Configuration structures
type CloudChannel struct {
	ChannelID   string       `json:"channelId"`
	ChannelName string       `json:"channelName"`
	Type        string       `json:"type"`
	SignalType  string       `json:"signalType"`
	Range       *RangeConfig `json:"range,omitempty"`
	Control     interface{}  `json:"control"`
}

type CloudCard struct {
	CardID   string         `json:"cardId"`
	CardName string         `json:"cardName,omitempty"`
	Channels []CloudChannel `json:"channels"`
}

type CloudZone struct {
	HubID    string      `json:"hubId,omitempty"`
	ZoneID   string      `json:"zoneId"`
	ZoneName string      `json:"zoneName,omitempty"`
	Cards    []CloudCard `json:"cards"`
}

type CloudPlatformConfig struct {
	UpdatedAt int64       `json:"updatedAt"`
	Zones     []CloudZone `json:"zones"`
}

// GetConfig request structure
type GetConfigRequest struct {
	RequestType string `json:"requestType"`
	Value       string `json:"value"`
}

// Periodic Data structures - IO Firmware format
type PeriodicChannel struct {
	ChannelID   string      `json:"channelId"`
	ChannelName string      `json:"channelName"`
	Value       interface{} `json:"value"`
	Unit        string      `json:"unit"`
	Status      string      `json:"status"`
}

type PeriodicCard struct {
	CardID   string            `json:"cardId"`
	CardName string            `json:"cardName,omitempty"`
	Channels []PeriodicChannel `json:"channels"`
}

type PeriodicDataIO struct {
	Timestamp int64          `json:"timestamp"`
	ZoneID    string         `json:"zoneId"`
	Cards     []PeriodicCard `json:"cards"`
}

// Periodic Data structures - Cloud Platform format
type CloudPeriodicZone struct {
	HubID    string         `json:"hubId,omitempty"`
	ZoneID   string         `json:"zoneId"`
	ZoneName string         `json:"zoneName,omitempty"`
	Cards    []PeriodicCard `json:"cards"`
}

type CloudPeriodicData struct {
	Timestamp int64               `json:"timestamp"`
	Zones     []CloudPeriodicZone `json:"zones"`
}

// Control Message structures
type ControlRequest struct {
	HubID       string      `json:"hubId"`
	ZoneID      string      `json:"zoneId"`
	CardID      string      `json:"cardId"`
	ChannelID   string      `json:"channelId"`
	ChannelName string      `json:"channelName"`
	Value       interface{} `json:"value"`
}

type ControlResponse struct {
	ZoneID    string `json:"zoneId"`
	CardID    string `json:"cardId"`
	ChannelID string `json:"channelId"`
	Status    string `json:"status"`
}

type WriteRequest struct {
	Cards []struct {
		CardID   string `json:"cardId"`
		Channels []struct {
			ChannelID   string      `json:"channelId"`
			ChannelName string      `json:"channelName"`
			Value       interface{} `json:"value"`
			Unit        string      `json:"unit"`
		} `json:"channels"`
	} `json:"cards"`
}

type ReadRequest struct {
	RequestType string `json:"requestType"`
	Value       string `json:"value"`
}

type CardsReadRequest struct {
	RequestType string   `json:"requestType"`
	Values      []string `json:"values"`
}

// Global variables for both MQTT clients
var serverConfig Config
var clientConfig Config
var mappingConfig MappingConfig
var serverClient mqtt.Client
var clientClient mqtt.Client

// PeriodicDataStore represents a store for periodic data with timestamp
type PeriodicDataStore struct {
	data      CloudPeriodicData
	timestamp time.Time
}

// Helper functions to get mappings from centralized mapping config
func getCardsToZonesMapping() map[string]string {
	if mappingConfig.CardZones != nil {
		return mappingConfig.CardZones
	}
	// Fallback to default mapping for backward compatibility
	return map[string]string{
		"CD1": "north_house",
		"CD2": "north_house",
		"CD3": "north_house",
		"CD4": "north_house",
		"CD5": "north_house",
		"CD6": "south_house",
		"CD7": "south_house",
		"CD8": "south_house",
		"CD9": "propagation_house",
	}
}

func getZoneIdToNamesMapping() map[string]string {
	if mappingConfig.ZoneIdNames != nil {
		return mappingConfig.ZoneIdNames
	}
	// Fallback to default mapping for backward compatibility
	return map[string]string{
		"north_house":       "North House",
		"south_house":       "South House",
		"propagation_house": "Propagation House",
	}
}

func getZoneIdToHubsMapping() map[string]string {
	if mappingConfig.ZoneIdHubs != nil {
		return mappingConfig.ZoneIdHubs
	}
	// Fallback to default mapping if not configured
	return map[string]string{
		"north_house":       "hub1",
		"south_house":       "hub1",
		"propagation_house": "hub1",
	}
}

// Helper function to get all cards for a given zone
func getCardsForZone(zoneId string) []string {
	cardsToZonesMapping := getCardsToZonesMapping()
	var cards []string

	for cardId, cardZone := range cardsToZonesMapping {
		if cardZone == zoneId {
			cards = append(cards, cardId)
		}
	}

	return cards
}

func getIOConfigPath() string {
	// Check if we're running tests
	testingEnv := os.Getenv("TESTING")
	if testingEnv == "true" {
		return "test/test-firmware-config.json"
	}
	return "firmware-config.json"
}

func getMappingConfigPath() string {
	// Check if we're running tests
	testingEnv := os.Getenv("TESTING")
	if testingEnv == "true" {
		return "test/test-mapping-config.json"
	}
	return "mapping-config.json"
}

func loadMappingConfig() error {
	configPath := getMappingConfigPath()

	file, err := os.Open(configPath)
	if err != nil {
		return fmt.Errorf("failed to open mapping config file %s: %v", configPath, err)
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&mappingConfig); err != nil {
		return fmt.Errorf("failed to decode mapping config: %v", err)
	}

	log.Printf("✓ Loaded mapping config from %s", configPath)
	return nil
}

const version = "0.0.10"

// Global variable to store periodic data
var periodicDataStore PeriodicDataStore
var periodicDataMutex sync.Mutex

// loadConfigValues loads configuration values from environment variables
func loadConfigValues() {
	// Load QoS
	if qosStr := os.Getenv("MQTT_QOS"); qosStr != "" {
		if qos, err := strconv.Atoi(qosStr); err == nil && qos >= 0 && qos <= 2 {
			currentQoS = byte(qos)
			log.Printf("QoS level: %d", currentQoS)
		} else {
			currentQoS = defaultQoS
			log.Printf("Invalid QoS value, using default: %d", currentQoS)
		}
	} else {
		currentQoS = defaultQoS
		log.Printf("QoS level: %d", currentQoS)
	}

	// Load PDS Time
	if pdsTimeStr := os.Getenv("PDS_TIME"); pdsTimeStr != "" {
		if pdsTime, err := strconv.Atoi(pdsTimeStr); err == nil && pdsTime > 0 {
			currentPDSTime = time.Duration(pdsTime) * time.Second
			log.Printf("PDS time: %d seconds", pdsTime)
		} else {
			currentPDSTime = defaultPDSTime * time.Second
			log.Printf("Invalid PDS time value, using default: %d seconds", defaultPDSTime)
		}
	} else {
		currentPDSTime = defaultPDSTime * time.Second
		log.Printf("PDS time: %d seconds", defaultPDSTime)
	}

	// Load log timestamp
	if logTimestampStr := os.Getenv("LOG_TIMESTAMP"); logTimestampStr != "" {
		if logTimestampEnv, err := strconv.ParseBool(logTimestampStr); err == nil {
			logTimestamp = logTimestampEnv
		} else {
			logTimestamp = defaultLogTimestamp
		}
	} else {
		logTimestamp = defaultLogTimestamp
	}
}

// getQoS returns the current QoS level
func getQoS() byte {
	return currentQoS
}

// getPDSTime returns the current PDS time
func getPDSTime() time.Duration {
	return currentPDSTime
}

// getLogTimestamp returns the current log timestamp
func getLogTimestamp() bool {
	return logTimestamp
}

// Simplified message handler for server topics
var serverMessageHandler mqtt.MessageHandler = func(client mqtt.Client, msg mqtt.Message) {
	log.Printf("Received on server topic '%s': %s\n", msg.Topic(), string(msg.Payload()))

	switch msg.Topic() {
	case TopicArgusGetConfig:
		handleGetConfigRequest(client, msg)
	case TopicArgusReqControlData:
		handleControlRequest(msg)
	case TopicArgusPullSensorData:
		handlePullSensorData(msg)
	}
}

// Simplified message handler for client topics
var clientMessageHandler mqtt.MessageHandler = func(client mqtt.Client, msg mqtt.Message) {
	log.Printf("Received on client topic '%s': %s\n", msg.Topic(), string(msg.Payload()))

	switch msg.Topic() {
	case TopicFirmwareReadDataPeriodicRes:
		handlePeriodicData(msg)
	case TopicFirmwareWriteDataRes:
		handleWriteResponse(msg)
	case TopicFirmwareReadDataRes:
		handleReadResponse(msg)
	}
}

// Helper function to transform periodic data IO to cloud format with zone mapping
func transformPeriodicDataToCloud(periodicDataIO PeriodicDataIO) CloudPeriodicData {
	// Create a map to group cards by zone
	zoneCards := make(map[string][]PeriodicCard)

	// Group cards by their zones using the mapping
	cardsToZonesMapping := getCardsToZonesMapping()
	for _, card := range periodicDataIO.Cards {
		zoneID := periodicDataIO.ZoneID // Default to zone ID from payload
		if mappedZoneID, exists := cardsToZonesMapping[card.CardID]; exists {
			zoneID = mappedZoneID
		} else {
			log.Printf("No zone mapping found for card %s, using zone ID from payload: %s", card.CardID, zoneID)
		}
		// Set CardName equal to CardID
		card.CardName = card.CardID
		zoneCards[zoneID] = append(zoneCards[zoneID], card)
	}

	// Create the cloud periodic data structure
	cloudPeriodicData := CloudPeriodicData{
		Timestamp: periodicDataIO.Timestamp,
		Zones:     make([]CloudPeriodicZone, 0),
	}

	// Convert the map to the required format
	zoneIdToNamesMapping := getZoneIdToNamesMapping()
	zoneIdToHubsMapping := getZoneIdToHubsMapping()
	for zoneID, cards := range zoneCards {
		cloudZone := CloudPeriodicZone{
			HubID:    zoneIdToHubsMapping[zoneID],
			ZoneID:   zoneID,
			ZoneName: zoneIdToNamesMapping[zoneID],
			Cards:    cards,
		}
		cloudPeriodicData.Zones = append(cloudPeriodicData.Zones, cloudZone)
	}

	return cloudPeriodicData
}

// Simplified periodic data handler
func handlePeriodicData(msg mqtt.Message) {
	var periodicDataIO PeriodicDataIO
	if err := json.Unmarshal(msg.Payload(), &periodicDataIO); err != nil {
		log.Printf("Error parsing periodic data: %v\n", err)
		return
	}

	// Transform the data using the helper function
	cloudPeriodicData := transformPeriodicDataToCloud(periodicDataIO)

	// Store the data with timestamp from cloudPeriodicData
	periodicDataMutex.Lock()
	timestamp := time.Now() // Default to current time
	if cloudPeriodicData.Timestamp > 0 {
		// Convert milliseconds to time.Time
		timestamp = time.Unix(0, cloudPeriodicData.Timestamp*int64(time.Millisecond))
	}
	periodicDataStore = PeriodicDataStore{
		data:      cloudPeriodicData,
		timestamp: timestamp,
	}
	periodicDataMutex.Unlock()

	// Publish immediately to periodic topic
	publishJSON(serverClient, TopicArgusPeriodic, cloudPeriodicData)
}

// Helper function to publish JSON data
func publishJSON(client mqtt.Client, topic string, data interface{}) {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		log.Printf("Error marshaling data: %v\n", err)
		return
	}

	if token := client.Publish(topic, getQoS(), false, jsonData); token.Wait() && token.Error() != nil {
		log.Printf("Failed to publish to %s: %v\n", topic, token.Error())
	} else {
		log.Printf("✓ Published to %s:\n%v\n", topic, string(jsonData))
	}
}

// Simplified pull sensor data handler
func handlePullSensorData(msg mqtt.Message) {
	var pullReq ReadRequest
	if err := json.Unmarshal(msg.Payload(), &pullReq); err != nil {
		log.Printf("Error parsing pull sensor request: %v\n", err)
		return
	}

	// Check if we have recent periodic data using PDS time from env var
	periodicDataMutex.Lock()
	hasRecentData := time.Since(periodicDataStore.timestamp) <= getPDSTime()
	storedData := periodicDataStore.data
	periodicDataMutex.Unlock()

	if hasRecentData {
		log.Println("returning recent periodic data")
		// Filter the stored data based on request type
		filteredData := filterPeriodicData(storedData, pullReq)

		// Check if filtered data has any zones
		if len(filteredData.Zones) > 0 {
			publishJSON(serverClient, TopicArgusPeriodic, filteredData)
			return
		}
		log.Println("filtered data has no zones, proceeding with normal request flow")
	}
	log.Println("no recent periodic data found, proceeding with normal request flow")

	// Handle different request types
	switch pullReq.RequestType {
	case "zone":
		// Get all cards for the requested zone
		zoneId := strings.TrimSpace(pullReq.Value)
		cards := getCardsForZone(zoneId)

		if len(cards) == 0 {
			log.Printf("No cards found for zone: %s", zoneId)
			return
		}

		log.Printf("Found %d cards for zone %s: %v", len(cards), zoneId, cards)

		// Create cards request
		cardsReq := CardsReadRequest{
			RequestType: "cards",
			Values:      cards,
		}

		publishJSON(clientClient, TopicFirmwareReadDataReq, cardsReq)

	default:
		// Handle other request types (card, channel, all) with existing logic
		readReq := ReadRequest{
			RequestType: pullReq.RequestType,
			Value:       pullReq.Value,
		}

		// Adjust value format based on request type
		switch pullReq.RequestType {
		case "card":
			if parts := strings.Split(pullReq.Value, "/"); len(parts) == 2 {
				readReq.Value = parts[1]
			}
		case "channel":
			if parts := strings.Split(pullReq.Value, "/"); len(parts) == 3 {
				readReq.Value = parts[1] + "/" + parts[2]
			}
		}

		publishJSON(clientClient, TopicFirmwareReadDataReq, readReq)
	}
}

// Helper function to filter periodic data based on request type
func filterPeriodicData(data CloudPeriodicData, req ReadRequest) CloudPeriodicData {
	filteredData := CloudPeriodicData{
		Timestamp: data.Timestamp,
		Zones:     make([]CloudPeriodicZone, 0),
	}

	zoneIdToNamesMapping := getZoneIdToNamesMapping()
	zoneIdToHubsMapping := getZoneIdToHubsMapping()

	switch req.RequestType {
	case "all":
		return data
	case "zone":
		zoneId := strings.TrimSpace(req.Value)
		if strings.Contains(zoneId, "/") {
			parts := strings.Split(zoneId, "/")
			if len(parts) > 1 {
				zoneId = strings.TrimSpace(parts[1])
			}
		}
		zoneId = strings.Trim(zoneId, "${}")

		for _, zone := range data.Zones {
			if zone.ZoneID == zoneId {
				filteredData.Zones = append(filteredData.Zones, zone)
				break
			}
		}
	case "card":
		parts := strings.Split(req.Value, "/")
		if len(parts) != 2 {
			return filteredData
		}
		zoneId := parts[0]
		cardId := parts[1]

		for _, zone := range data.Zones {
			if zone.ZoneID == zoneId {
				filteredZone := CloudPeriodicZone{
					HubID:    zoneIdToHubsMapping[zone.ZoneID],
					ZoneID:   zone.ZoneID,
					ZoneName: zoneIdToNamesMapping[zone.ZoneID],
					Cards:    make([]PeriodicCard, 0),
				}
				for _, card := range zone.Cards {
					if card.CardID == cardId {
						filteredZone.Cards = append(filteredZone.Cards, card)
						break
					}
				}
				if len(filteredZone.Cards) > 0 {
					filteredData.Zones = append(filteredData.Zones, filteredZone)
				}
				break
			}
		}
	case "channel":
		parts := strings.Split(req.Value, "/")
		if len(parts) != 3 {
			return filteredData
		}
		zoneId := parts[0]
		cardId := parts[1]
		channelId := parts[2]

		for _, zone := range data.Zones {
			if zone.ZoneID == zoneId {
				filteredZone := CloudPeriodicZone{
					HubID:    zoneIdToHubsMapping[zone.ZoneID],
					ZoneID:   zone.ZoneID,
					ZoneName: zoneIdToNamesMapping[zone.ZoneID],
					Cards:    make([]PeriodicCard, 0),
				}
				for _, card := range zone.Cards {
					if card.CardID == cardId {
						filteredCard := PeriodicCard{
							CardID:   card.CardID,
							Channels: make([]PeriodicChannel, 0),
						}
						for _, channel := range card.Channels {
							if channel.ChannelID == channelId {
								filteredCard.Channels = append(filteredCard.Channels, channel)
								break
							}
						}
						if len(filteredCard.Channels) > 0 {
							filteredZone.Cards = append(filteredZone.Cards, filteredCard)
						}
						break
					}
				}
				if len(filteredZone.Cards) > 0 {
					filteredData.Zones = append(filteredData.Zones, filteredZone)
				}
				break
			}
		}
	}

	return filteredData
}

// Connection lost handlers
var serverConnectLostHandler mqtt.ConnectionLostHandler = func(client mqtt.Client, err error) {
	log.Printf("Server connection lost: %v\n", err)
	log.Println("Attempting to reconnect server...")
}

var clientConnectLostHandler mqtt.ConnectionLostHandler = func(client mqtt.Client, err error) {
	log.Printf("Client connection lost: %v\n", err)
	log.Println("Attempting to reconnect client...")
}

// On connect handlers
var serverOnConnectHandler mqtt.OnConnectHandler = func(client mqtt.Client) {
	// log.Println("\nSuccessfully connected to MQTT broker (server)")

	// Subscribe to server topics when connected
	subscribeToServerTopics(client)

	// Publish initial config on startup
	publishInitialConfig(client)
}

var clientOnConnectHandler mqtt.OnConnectHandler = func(client mqtt.Client) {
	// log.Println("\nSuccessfully connected to MQTT broker (client)")

	// Subscribe to client topics when connected
	subscribeToClientTopics(client)
}

// Retry configuration
const (
	maxRetries     = 5
	initialBackoff = 1 * time.Second
	maxBackoff     = 10 * time.Second
)

// Simplified MQTT client creation with common options
func createMQTTClient(config Config, clientID string, messageHandler mqtt.MessageHandler, connectHandler mqtt.OnConnectHandler, lostHandler mqtt.ConnectionLostHandler) mqtt.Client {
	opts := mqtt.NewClientOptions()
	brokerURL := fmt.Sprintf("%s://%s:%d", config.MQTT.Protocol, config.MQTT.Broker, config.MQTT.Port)

	opts.AddBroker(brokerURL)
	opts.SetUsername(config.MQTT.Username)
	opts.SetPassword(config.MQTT.Password)
	opts.SetClientID(clientID)
	opts.SetTLSConfig(&tls.Config{InsecureSkipVerify: true})
	opts.SetDefaultPublishHandler(messageHandler)
	opts.OnConnect = connectHandler
	opts.OnConnectionLost = lostHandler

	// Common connection settings
	opts.SetKeepAlive(60 * time.Second)
	opts.SetPingTimeout(10 * time.Second)
	opts.SetConnectTimeout(30 * time.Second)
	opts.SetAutoReconnect(true)
	opts.SetMaxReconnectInterval(10 * time.Second)

	return mqtt.NewClient(opts)
}

// Simplified connection with retry
func connectWithRetry(client mqtt.Client, clientName string) error {
	backoff := initialBackoff
	for attempt := 1; attempt <= maxRetries; attempt++ {
		log.Printf("Connecting to MQTT broker (%s) - attempt %d/%d...\n", clientName, attempt, maxRetries)

		if token := client.Connect(); token.Wait() && token.Error() != nil {
			log.Printf("Connection failed: %v\n", token.Error())

			if attempt < maxRetries {
				log.Printf("Retrying in %v...\n", backoff)
				time.Sleep(backoff)
				backoff = min(backoff*2, maxBackoff)
				continue
			}
			return fmt.Errorf("failed to connect after %d attempts: %v", maxRetries, token.Error())
		}

		log.Printf("Successfully connected to MQTT broker (%s)\n", clientName)
		return nil
	}
	return fmt.Errorf("failed to connect after %d attempts", maxRetries)
}

// Helper function for min
func min(a, b time.Duration) time.Duration {
	if a < b {
		return a
	}
	return b
}

func loadTopicConstants() {
	// Load server topics
	if val, ok := serverConfig.SubscribeTopics["TopicArgusGetConfig"]; ok {
		TopicArgusGetConfig = val
	}
	if val, ok := serverConfig.SubscribeTopics["TopicArgusReqControlData"]; ok {
		TopicArgusReqControlData = val
	}
	if val, ok := serverConfig.SubscribeTopics["TopicArgusPullSensorData"]; ok {
		TopicArgusPullSensorData = val
	}
	if val, ok := serverConfig.PublishTopics["TopicArgusConfig"]; ok {
		TopicArgusConfig = val
	}
	if val, ok := serverConfig.PublishTopics["TopicArgusPeriodic"]; ok {
		TopicArgusPeriodic = val
	}
	if val, ok := serverConfig.PublishTopics["TopicArgusResponseControlData"]; ok {
		TopicArgusResponseControlData = val
	}

	// Load client topics
	if val, ok := clientConfig.SubscribeTopics["TopicFirmwareReadDataRes"]; ok {
		TopicFirmwareReadDataRes = val
	}
	if val, ok := clientConfig.SubscribeTopics["TopicFirmwareWriteDataRes"]; ok {
		TopicFirmwareWriteDataRes = val
	}
	if val, ok := clientConfig.SubscribeTopics["TopicFirmwareReadDataPeriodicRes"]; ok {
		TopicFirmwareReadDataPeriodicRes = val
	}
	if val, ok := clientConfig.PublishTopics["TopicFirmwareReadDataReq"]; ok {
		TopicFirmwareReadDataReq = val
	}
	if val, ok := clientConfig.PublishTopics["TopicFirmwareWriteDataReq"]; ok {
		TopicFirmwareWriteDataReq = val
	}
}

func loadConfigs() error {
	// Determine which config files to use
	serverConfigFile := "config-server.json"
	clientConfigFile := "config-client.json"

	// Check if we're running tests
	testingEnv := os.Getenv("TESTING")
	if testingEnv == "true" {
		serverConfigFile = "test/test-config-server.json"
		clientConfigFile = "test/test-config-client.json"
	}

	// Load server config
	serverData, err := os.ReadFile(serverConfigFile)
	if err != nil {
		return fmt.Errorf("failed to read server config file: %v", err)
	}

	err = json.Unmarshal(serverData, &serverConfig)
	if err != nil {
		return fmt.Errorf("failed to parse server config JSON: %v", err)
	}

	// Load client config
	clientData, err := os.ReadFile(clientConfigFile)
	if err != nil {
		return fmt.Errorf("failed to read client config file: %v", err)
	}

	err = json.Unmarshal(clientData, &clientConfig)
	if err != nil {
		return fmt.Errorf("failed to parse client config JSON: %v", err)
	}

	// Load mapping config
	if err := loadMappingConfig(); err != nil {
		return fmt.Errorf("failed to load mapping config: %v", err)
	}

	// Load topic constants from configs
	loadTopicConstants()

	return nil
}

func subscribeToServerTopics(client mqtt.Client) {
	log.Printf("\nSubscribing to %d server topics...\n", len(serverConfig.SubscribeTopics))

	for topicName, topic := range serverConfig.SubscribeTopics {
		if token := client.Subscribe(topic, getQoS(), nil); token.Wait() && token.Error() != nil {
			log.Printf("Failed to subscribe to server topic '%s' (%s): %v", topicName, topic, token.Error())
		} else {
			log.Printf("✓ Subscribed to server topic: %s\n", topic)
		}
	}
}

func subscribeToClientTopics(client mqtt.Client) {
	log.Printf("\nSubscribing to %d client topics...\n", len(clientConfig.SubscribeTopics))

	for _, topic := range clientConfig.SubscribeTopics {
		if token := client.Subscribe(topic, getQoS(), nil); token.Wait() && token.Error() != nil {
			log.Printf("Failed to subscribe to client topic '%s': %v", topic, token.Error())
		} else {
			log.Printf("✓ Subscribed to client topic: %s\n", topic)
		}
	}
}

func unsubscribeFromTopics(client mqtt.Client, topics map[string]string) {
	log.Println("Unsubscribing from topics...")

	for _, topic := range topics {
		if token := client.Unsubscribe(topic); token.Wait() && token.Error() != nil {
			log.Printf("Failed to unsubscribe from topic '%s': %v", topic, token.Error())
		}
	}
}

func loadIOFirmwareConfig(filename string) (*IOFirmwareConfig, error) {
	// Check if file exists, if not throw error
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return nil, fmt.Errorf("IO Firmware config file not found at %s", filename)
	}

	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read IO firmware config: %v", err)
	}

	var ioConfig IOFirmwareConfig
	err = json.Unmarshal(data, &ioConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to parse IO firmware config: %v", err)
	}

	return &ioConfig, nil
}

func transformToCloudPlatformConfig(ioConfig *IOFirmwareConfig) *CloudPlatformConfig {
	cloudConfig := &CloudPlatformConfig{
		UpdatedAt: time.Now().UnixMilli(),
		Zones:     []CloudZone{},
	}

	// Create a map to group cards by their zones
	zoneCards := make(map[string][]CloudCard)

	// Get mappings from config
	cardsToZonesMapping := getCardsToZonesMapping()
	zoneIdToNamesMapping := getZoneIdToNamesMapping()
	zoneIdToHubsMapping := getZoneIdToHubsMapping()

	// Process all cards and group them by their mapped zones
	for _, port := range ioConfig.Ports {
		for _, card := range port.Cards {
			cloudCard := CloudCard{
				CardID:   card.CardID,
				CardName: card.CardID,
				Channels: []CloudChannel{},
			}

			for _, channel := range card.Channels {
				cloudChannel := CloudChannel{
					ChannelID:   channel.ChannelID,
					ChannelName: channel.ChannelName,
					Control:     nil,
				}

				// Transform type: input -> read, output -> write
				if channel.Type == "input" {
					cloudChannel.Type = "read"
				} else if channel.Type == "output" {
					cloudChannel.Type = "write"
				}

				// Transform signal type: remove specific voltage/current suffixes for analog
				if strings.HasPrefix(channel.SignalType, "digital") {
					cloudChannel.SignalType = "digital"
					cloudChannel.Range = nil
				} else if strings.HasPrefix(channel.SignalType, "analog") {
					cloudChannel.SignalType = "analog"
					cloudChannel.Range = channel.Range
				}

				cloudCard.Channels = append(cloudCard.Channels, cloudChannel)
			}

			// Determine the zone for this card using the mapping
			zoneID := ioConfig.ZoneName // Default to zone from config
			if mappedZoneID, exists := cardsToZonesMapping[card.CardID]; exists {
				zoneID = mappedZoneID
			} else {
				log.Printf("No zone mapping found for card %s, using zone ID from config: %s", card.CardID, zoneID)
			}

			// Add the card to the appropriate zone
			zoneCards[zoneID] = append(zoneCards[zoneID], cloudCard)
		}
	}

	// Convert the map to the required format
	for zoneID, cards := range zoneCards {
		cloudZone := CloudZone{
			HubID:    zoneIdToHubsMapping[zoneID],
			ZoneID:   zoneID,
			ZoneName: zoneIdToNamesMapping[zoneID],
			Cards:    cards,
		}
		cloudConfig.Zones = append(cloudConfig.Zones, cloudZone)
	}

	return cloudConfig
}

func handleGetConfigRequest(client mqtt.Client, msg mqtt.Message) {
	var getConfigReq GetConfigRequest
	if err := json.Unmarshal(msg.Payload(), &getConfigReq); err != nil {
		log.Printf("Error parsing getConfig request: %v\n", err)
		return
	}

	log.Printf("Processing getConfig request: type=%s, value=%s\n",
		getConfigReq.RequestType, getConfigReq.Value)

	// Load IO firmware config
	ioConfig, err := loadIOFirmwareConfig(getIOConfigPath())
	if err != nil {
		log.Printf("Error loading IO firmware config: %v\n", err)
		return
	}

	// Transform to cloud platform format
	cloudConfig := transformToCloudPlatformConfig(ioConfig)

	// Filter based on request type
	if getConfigReq.RequestType == "zone" {
		// Extract zone ID from value (format: "* / ${zoneId}" or just zoneId)
		zoneId := strings.TrimSpace(getConfigReq.Value)
		if strings.Contains(zoneId, "/") {
			parts := strings.Split(zoneId, "/")
			if len(parts) > 1 {
				zoneId = strings.TrimSpace(parts[1])
			}
		}
		zoneId = strings.Trim(zoneId, "${}")

		// Filter zones
		filteredZones := []CloudZone{}
		for _, zone := range cloudConfig.Zones {
			if zone.ZoneID == zoneId {
				filteredZones = append(filteredZones, zone)
				break
			}
		}
		cloudConfig.Zones = filteredZones
	}
	// For "all" request type, we return all zones (no filtering needed)

	// Publish the transformed config
	configTopic := TopicArgusConfig
	configJSON, err := json.MarshalIndent(cloudConfig, "", "  ")
	if err != nil {
		log.Printf("Error marshaling config response: %v\n", err)
		return
	}

	if token := client.Publish(configTopic, getQoS(), false, configJSON); token.Wait() && token.Error() != nil {
		log.Printf("Failed to publish config response: %v\n", token.Error())
	} else {
		log.Printf("✓ Published config response to '%s'\n", configTopic)
	}
}

func publishInitialConfig(client mqtt.Client) {
	// Load and publish initial config on startup
	ioConfig, err := loadIOFirmwareConfig(getIOConfigPath())
	if err != nil {
		log.Printf("Warning: Cannot load IO firmware config for initial publish: %v\n", err)
		return
	}

	cloudConfig := transformToCloudPlatformConfig(ioConfig)
	configTopic := TopicArgusConfig

	configJSON, err := json.MarshalIndent(cloudConfig, "", "  ")
	if err != nil {
		log.Printf("Error marshaling initial config: %v\n", err)
		return
	}

	if token := client.Publish(configTopic, getQoS(), false, configJSON); token.Wait() && token.Error() != nil {
		log.Printf("Failed to publish initial config: %v\n", token.Error())
	} else {
		log.Printf("✓ Published initial config to '%s'\n", configTopic)
	}
}

func handleControlRequest(msg mqtt.Message) {
	var controlReq ControlRequest
	if err := json.Unmarshal(msg.Payload(), &controlReq); err != nil {
		log.Printf("Error parsing control request: %v\n", err)
		return
	}

	// Validation 1: Check if cardId belongs to the requested zoneId
	cardsToZonesMapping := getCardsToZonesMapping()
	expectedZoneID, cardExists := cardsToZonesMapping[controlReq.CardID]
	if !cardExists {
		log.Printf("Ignoring control request: cardId '%s' not found in mapping", controlReq.CardID)
		return
	}
	if expectedZoneID != controlReq.ZoneID {
		log.Printf("Ignoring control request: cardId '%s' does not belongs to zone '%s'",
			controlReq.CardID, controlReq.ZoneID)
		return
	}

	// Validation 2: Check if zoneId belongs to the requested hubId
	zoneIdToHubsMapping := getZoneIdToHubsMapping()
	expectedHubID, zoneExists := zoneIdToHubsMapping[controlReq.ZoneID]
	if !zoneExists {
		log.Printf("Ignoring control request: zoneId '%s' not found in hub mapping", controlReq.ZoneID)
		return
	}
	if expectedHubID != controlReq.HubID {
		log.Printf("Ignoring control request: zoneId '%s' does not belong to hub '%s'",
			controlReq.ZoneID, controlReq.HubID)
		return
	}

	log.Printf("✓ Validation passed: cardId '%s' belongs to zoneId '%s' and zoneId belongs to hubId '%s'",
		controlReq.CardID, controlReq.ZoneID, controlReq.HubID)

	// Create write request using channelName from the incoming message
	writeReq := WriteRequest{
		Cards: []struct {
			CardID   string `json:"cardId"`
			Channels []struct {
				ChannelID   string      `json:"channelId"`
				ChannelName string      `json:"channelName"`
				Value       interface{} `json:"value"`
				Unit        string      `json:"unit"`
			} `json:"channels"`
		}{
			{
				CardID: controlReq.CardID,
				Channels: []struct {
					ChannelID   string      `json:"channelId"`
					ChannelName string      `json:"channelName"`
					Value       interface{} `json:"value"`
					Unit        string      `json:"unit"`
				}{
					{
						ChannelID:   controlReq.ChannelID,
						ChannelName: controlReq.ChannelName,
						Value:       controlReq.Value,
						Unit:        "C", // Default unit, can be made configurable
					},
				},
			},
		},
	}

	// Publish write request using clientClient since it's not an argus/ topic
	writeReqJSON, err := json.MarshalIndent(writeReq, "", "  ")
	if err != nil {
		log.Printf("Error marshaling write request: %v\n", err)
		return
	}

	log.Printf("Publishing write request to %s:\n%s\n", TopicFirmwareWriteDataReq, string(writeReqJSON))

	if token := clientClient.Publish(TopicFirmwareWriteDataReq, getQoS(), false, writeReqJSON); token.Wait() && token.Error() != nil {
		log.Printf("Failed to publish write request: %v\n", token.Error())
	} else {
		log.Printf("✓ Published write request to %s\n", TopicFirmwareWriteDataReq)
	}
}

func handleWriteResponse(msg mqtt.Message) {
	log.Printf("Received write response:\n%s\n", string(msg.Payload()))

	// Parse the incoming data
	var periodicDataIO PeriodicDataIO
	if err := json.Unmarshal(msg.Payload(), &periodicDataIO); err != nil {
		log.Printf("Error parsing write response data: %v\n", err)
		return
	}

	// Transform the data using the helper function
	cloudPeriodicData := transformPeriodicDataToCloud(periodicDataIO)

	// Convert to JSON for publishing
	cloudDataJSON, err := json.MarshalIndent(cloudPeriodicData, "", "  ")
	if err != nil {
		log.Printf("Error marshaling cloud periodic data: %v\n", err)
		return
	}

	// Republish to responseControlData topic using serverClient since it's an argus/ topic
	if token := serverClient.Publish(TopicArgusResponseControlData, getQoS(), false, cloudDataJSON); token.Wait() && token.Error() != nil {
		log.Printf("Failed to publish control response: %v\n", token.Error())
	} else {
		log.Printf("✓ Republished write response to %s:\n%v\n", TopicArgusResponseControlData, string(cloudDataJSON))
	}
}

func handleReadResponse(msg mqtt.Message) {
	log.Printf("Received read response on topic '%s':\n%s\n", msg.Topic(), string(msg.Payload()))

	// Parse the incoming data
	var periodicDataIO PeriodicDataIO
	if err := json.Unmarshal(msg.Payload(), &periodicDataIO); err != nil {
		log.Printf("Error parsing read response data: %v\n", err)
		return
	}

	// Transform the data using the helper function
	cloudPeriodicData := transformPeriodicDataToCloud(periodicDataIO)

	// Convert to JSON for publishing
	cloudDataJSON, err := json.MarshalIndent(cloudPeriodicData, "", "  ")
	if err != nil {
		log.Printf("Error marshaling cloud periodic data: %v\n", err)
		return
	}

	// Republish to periodic topic using serverClient since it's an argus/ topic
	if token := serverClient.Publish(TopicArgusPeriodic, getQoS(), false, cloudDataJSON); token.Wait() && token.Error() != nil {
		log.Printf("Failed to publish read response: %v\n", token.Error())
	} else {
		log.Printf("✓ Successfully republished read response to %s:\n%v\n", TopicArgusPeriodic, string(cloudDataJSON))
	}
}

// Helper function to print configurations
func printConfigs() {
	log.Printf("Loaded MQTT configurations:\n")
	for _, cfg := range []struct {
		name string
		cfg  Config
	}{
		{"Server", serverConfig},
		{"Client", clientConfig},
	} {
		log.Printf("%s Config:\n", cfg.name)
		log.Printf("  Broker: %s:%d\n", cfg.cfg.MQTT.Broker, cfg.cfg.MQTT.Port)
		log.Printf("  Username: %s\n", cfg.cfg.MQTT.Username)
		log.Printf("  Client ID: %s\n", cfg.cfg.MQTT.ClientID)
		log.Printf("  Subscribe topics: %d\n", len(cfg.cfg.SubscribeTopics))
		log.Printf("  Publish topics: %d\n", len(cfg.cfg.PublishTopics))
		log.Println()
	}
}

// Helper function to print subscribed topics
func printSubscribedTopics() {
	log.Printf("\nMQTT clients are running and listening for messages...\n")
	for _, cfg := range []struct {
		name   string
		topics map[string]string
	}{
		{"Server", serverConfig.SubscribeTopics},
		{"Client", clientConfig.SubscribeTopics},
	} {
		log.Printf("%s subscribed topics:\n", cfg.name)
		for topicName, topic := range cfg.topics {
			log.Printf("  - %s: %s\n", topicName, topic)
		}
		log.Println()
	}
}

// Helper function for graceful shutdown
func shutdown() {
	log.Println("\nShutting down...")
	unsubscribeFromTopics(serverClient, serverConfig.SubscribeTopics)
	unsubscribeFromTopics(clientClient, clientConfig.SubscribeTopics)
	serverClient.Disconnect(250)
	clientClient.Disconnect(250)
	log.Println("Disconnected from MQTT brokers")
}

func main() {
	log.SetFlags(0)

	log.Print("Starting Argus Cloud Connector... v", version)

	_ = godotenv.Load()

	// Load configuration values
	loadConfigValues()

	if getLogTimestamp() {
		log.SetFlags(log.LstdFlags)
	}

	// Load configurations
	if err := loadConfigs(); err != nil {
		log.Fatalf("Error loading MQTT configs: %v", err)
	}

	printConfigs()

	// Create and connect MQTT clients
	serverClient = createMQTTClient(serverConfig, serverConfig.MQTT.ClientID+"-server",
		serverMessageHandler, serverOnConnectHandler, serverConnectLostHandler)
	clientClient = createMQTTClient(clientConfig, clientConfig.MQTT.ClientID+"-client",
		clientMessageHandler, clientOnConnectHandler, clientConnectLostHandler)

	// Connect clients with retry
	if err := connectWithRetry(serverClient, "server"); err != nil {
		log.Fatalf("Failed to connect to MQTT broker (server): %v", err)
	}
	if err := connectWithRetry(clientClient, "client"); err != nil {
		log.Fatalf("Failed to connect to MQTT broker (client): %v", err)
	}

	// !NOTE: Wait for connections to stabilize
	time.Sleep(1 * time.Second)

	// Set up graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	printSubscribedTopics()
	log.Println("Press Ctrl+C to exit...")

	// Wait for interrupt signal
	<-c
	shutdown()
}
