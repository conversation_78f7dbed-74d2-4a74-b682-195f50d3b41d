package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/joho/godotenv"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

const version = "0.0.10"

// Global variables for MQTT clients
var serverWrapper *MQTTClientWrapper
var clientWrapper *MQTTClientWrapper
var messageHandlers *MessageHandlers

// Connection lost handlers
var serverConnectLostHandler mqtt.ConnectionLostHandler = func(client mqtt.Client, err error) {
	log.Printf("Server connection lost: %v\n", err)
	log.Println("Attempting to reconnect server...")
}

var clientConnectLostHandler mqtt.ConnectionLostHandler = func(client mqtt.Client, err error) {
	log.Printf("Client connection lost: %v\n", err)
	log.Println("Attempting to reconnect client...")
}

// On connect handlers
var serverOnConnectHandler mqtt.OnConnectHandler = func(client mqtt.Client) {
	// Subscribe to server topics when connected
	serverWrapper.SubscribeToTopics()

	// Publish initial config on startup
	messageHandlers.PublishInitialConfig(client)
}

var clientOnConnectHandler mqtt.OnConnectHandler = func(client mqtt.Client) {
	// Subscribe to client topics when connected
	clientWrapper.SubscribeToTopics()
}

// Helper function for graceful shutdown
func shutdown() {
	log.Println("\nShutting down...")
	serverWrapper.UnsubscribeFromTopics()
	clientWrapper.UnsubscribeFromTopics()
	serverWrapper.Disconnect()
	clientWrapper.Disconnect()
	log.Println("Disconnected from MQTT brokers")
}

func main() {
	log.SetFlags(0)

	log.Print("Starting Argus Cloud Connector... v", version)

	_ = godotenv.Load()

	// Initialize configuration manager
	configManager := NewConfigManager()

	// Load configuration values
	configManager.LoadConfigValues()

	if GetLogTimestamp() {
		log.SetFlags(log.LstdFlags)
	}

	// Load configurations
	if err := configManager.LoadConfigs(); err != nil {
		log.Fatalf("Error loading MQTT configs: %v", err)
	}

	configManager.PrintConfigs()

	// Create MQTT client wrappers
	serverWrapper = NewMQTTClientWrapper(serverConfig, serverConfig.MQTT.ClientID+"-server",
		nil, serverOnConnectHandler, serverConnectLostHandler, "Server")
	clientWrapper = NewMQTTClientWrapper(clientConfig, clientConfig.MQTT.ClientID+"-client",
		nil, clientOnConnectHandler, clientConnectLostHandler, "Client")

	// Initialize message handlers with the actual clients
	messageHandlers = NewMessageHandlers(serverWrapper.GetClient(), clientWrapper.GetClient())

	// Set the message handlers
	serverWrapper.SetMessageHandler(messageHandlers.ServerMessageHandler, serverConfig.MQTT.ClientID+"-server")
	clientWrapper.SetMessageHandler(messageHandlers.ClientMessageHandler, clientConfig.MQTT.ClientID+"-client")

	// Connect clients with retry
	if err := serverWrapper.Connect(); err != nil {
		log.Fatalf("Failed to connect to MQTT broker (server): %v", err)
	}
	if err := clientWrapper.Connect(); err != nil {
		log.Fatalf("Failed to connect to MQTT broker (client): %v", err)
	}

	// !NOTE: Wait for connections to stabilize
	time.Sleep(1 * time.Second)

	// Set up graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	PrintSubscribedTopics(serverWrapper, clientWrapper)
	log.Println("Press Ctrl+C to exit...")

	// Wait for interrupt signal
	<-c
	shutdown()
}
